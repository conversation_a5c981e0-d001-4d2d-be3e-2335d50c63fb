package org.jeecg.modules.docsystem.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.es.JeecgElasticsearchTemplate;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.docsystem.dto.DocSearchDTO;
import org.jeecg.modules.docsystem.entity.DocLibrary;
import org.jeecg.modules.docsystem.service.IDocElasticsearchService;
import org.jeecg.modules.docsystem.service.IDocLibraryService;
import org.jeecg.modules.docsystem.vo.DocSearchResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于Elasticsearch的文档搜索服务实现
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2025-08-07
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "jeecg.elasticsearch", name = "cluster-nodes")
public class DocElasticsearchServiceImpl implements IDocElasticsearchService {

    @Autowired
    private JeecgElasticsearchTemplate elasticsearchTemplate;
    
    @Autowired
    private IDocLibraryService docLibraryService;

    /** 文档索引名称 */
    private static final String DOC_INDEX_NAME = "doc_library";
    /** 文档类型名称（ES 8.0+不再使用，保留兼容性） */
    private static final String DOC_TYPE_NAME = "_doc";

    @Override
    public boolean createDocumentIndex() {
        try {
            // 先删除已存在的索引（如果存在）
            deleteIndexIfExists();

            // 创建索引
            boolean created = elasticsearchTemplate.createIndex(DOC_INDEX_NAME);
            if (created) {
                log.info("文档索引创建成功: {}", DOC_INDEX_NAME);

                // 创建后设置正确的映射
                boolean mappingSet = setDocumentMapping();
                if (mappingSet) {
                    log.info("文档映射设置成功");
                } else {
                    log.warn("文档映射设置失败，但索引已创建");
                }
            }
            return created;
        } catch (Exception e) {
            log.error("创建文档索引失败", e);
            return false;
        }
    }

    @Override
    public boolean indexDocument(DocLibrary document) {
        if (document == null || oConvertUtils.isEmpty(document.getId())) {
            return false;
        }

        try {
            // 转换为JSON对象
            JSONObject docJson = convertDocumentToJson(document);
            
            // 保存到ES
            boolean saved = elasticsearchTemplate.saveOrUpdate(DOC_INDEX_NAME, DOC_TYPE_NAME, document.getId(), docJson);
            
            if (saved) {
                log.debug("文档索引成功: {}", document.getId());
            }
            
            return saved;
        } catch (Exception e) {
            log.error("索引文档失败: " + document.getId(), e);
            return false;
        }
    }

    @Override
    public boolean indexDocuments(List<DocLibrary> documents) {
        if (documents == null || documents.isEmpty()) {
            return false;
        }

        try {
            // 优化：使用真正的批量索引提高效率
            if (documents.size() > 100) {
                // 大批量数据分批处理，每批100个
                return indexDocumentsInBatches(documents, 100);
            } else {
                // 小批量直接使用ES批量API
                return indexDocumentsBatch(documents);
            }
        } catch (Exception e) {
            log.error("批量索引文档失败", e);
            return false;
        }
    }

    /**
     * 分批索引文档
     */
    private boolean indexDocumentsInBatches(List<DocLibrary> documents, int batchSize) {
        int totalCount = documents.size();
        int successBatches = 0;
        int totalBatches = (totalCount + batchSize - 1) / batchSize;

        for (int i = 0; i < totalCount; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalCount);
            List<DocLibrary> batch = documents.subList(i, endIndex);

            try {
                boolean batchSuccess = indexDocumentsBatch(batch);
                if (batchSuccess) {
                    successBatches++;
                }
                log.info("批量索引进度: {}/{}, 当前批次: {}", successBatches, totalBatches, batch.size());
            } catch (Exception e) {
                log.warn("批次索引失败: 第{}批, 大小: {}", (i / batchSize + 1), batch.size(), e);
            }
        }

        boolean success = successBatches > 0;
        log.info("分批索引完成: 成功批次 {}/{}, 总文档数: {}", successBatches, totalBatches, totalCount);
        return success;
    }

    /**
     * 使用ES批量API索引文档
     */
    private boolean indexDocumentsBatch(List<DocLibrary> documents) {
        try {
            // 转换为JSONArray格式
            JSONArray dataArray = new JSONArray();
            for (DocLibrary doc : documents) {
                JSONObject docJson = convertDocumentToJson(doc);
                dataArray.add(docJson);
            }

            // 使用ES批量API
            boolean result = elasticsearchTemplate.saveBatch(DOC_INDEX_NAME, DOC_TYPE_NAME, dataArray);

            if (result) {
                log.debug("批量索引成功，文档数量: {}", documents.size());
            } else {
                log.warn("批量索引失败，文档数量: {}", documents.size());
            }

            return result;
        } catch (Exception e) {
            log.error("批量索引执行失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteDocumentIndex(String documentId) {
        if (oConvertUtils.isEmpty(documentId)) {
            return false;
        }

        try {
            boolean deleted = elasticsearchTemplate.delete(DOC_INDEX_NAME, DOC_TYPE_NAME, documentId);

            if (deleted) {
                log.debug("删除文档索引成功: {}", documentId);
            }

            return deleted;
        } catch (Exception e) {
            log.error("删除文档索引失败: " + documentId, e);
            return false;
        }
    }

    @Override
    public IPage<DocSearchResultVO> searchDocuments(DocSearchDTO searchDTO) {
        try {
            log.info("开始ES搜索，关键词：{}", searchDTO.getKeyword());

            // 检查ES模板是否可用
            if (elasticsearchTemplate == null) {
                log.error("ElasticsearchTemplate未注入，ES搜索不可用");
                throw new RuntimeException("ElasticsearchTemplate未注入");
            }

            // 构建查询条件
            JSONObject queryObject = buildSearchQuery(searchDTO);
            log.info("ES查询条件：{}", queryObject.toJSONString());

            // 执行搜索 - ES 9.x兼容：使用_doc作为type，JeecgElasticsearchTemplate会自动处理
            JSONObject searchResult = elasticsearchTemplate.search(DOC_INDEX_NAME, DOC_TYPE_NAME, queryObject);
            log.info("ES搜索结果：{}", searchResult != null ? searchResult.toJSONString() : "null");

            // 解析搜索结果
            IPage<DocSearchResultVO> result = parseSearchResult(searchResult, searchDTO);
            log.info("解析后结果数量：{}", result.getRecords().size());

            return result;

        } catch (Exception e) {
            log.error("Elasticsearch搜索失败，关键词：{}，错误：{}", searchDTO.getKeyword(), e.getMessage(), e);
            // 返回空结果
            int pageNo = searchDTO.getPageNo() == null ? 1 : searchDTO.getPageNo();
            int pageSize = searchDTO.getPageSize() == null ? 10 : searchDTO.getPageSize();
            return new Page<>(pageNo, pageSize);
        }
    }

    @Override
    public boolean rebuildAllDocumentIndex() {
        try {
            log.info("开始重建文档索引...");
            
            // 删除现有索引
            elasticsearchTemplate.removeIndex(DOC_INDEX_NAME);
            
            // 创建新索引
            createDocumentIndex();
            
            // 获取所有文档
            List<DocLibrary> allDocuments = docLibraryService.list();
            
            if (allDocuments != null && !allDocuments.isEmpty()) {
                // 批量索引所有文档
                boolean indexed = indexDocuments(allDocuments);
                
                if (indexed) {
                    log.info("文档索引重建成功，索引文档数量: {}", allDocuments.size());
                    return true;
                } else {
                    log.error("批量索引文档失败");
                    return false;
                }
            } else {
                log.info("没有文档需要索引");
                return true;
            }
            
        } catch (Exception e) {
            log.error("重建文档索引失败", e);
            return false;
        }
    }

    /**
     * 将文档对象转换为JSON
     */
    private JSONObject convertDocumentToJson(DocLibrary document) {
        JSONObject docJson = new JSONObject();
        
        docJson.put("id", document.getId());
        docJson.put("title", document.getTitle());
        docJson.put("description", document.getDescription());
        docJson.put("fileName", document.getFileName());
        docJson.put("fileType", document.getFileType());
        docJson.put("fileExt", document.getFileExt());
        docJson.put("fileSize", document.getFileSize());
        docJson.put("categoryId", document.getCategoryId());
        docJson.put("status", document.getStatus());

        // 布尔字段转换 - ES需要true/false而不是1/0
        docJson.put("isPublic", convertToBoolean(document.getIsPublic()));
        docJson.put("isFeatured", convertToBoolean(document.getIsFeatured()));

        docJson.put("ratingScore", document.getRatingScore());
        docJson.put("downloadCount", document.getDownloadCount());
        docJson.put("viewCount", document.getViewCount());
        docJson.put("createBy", document.getCreateBy());

        // 时间格式转换 - ES需要特定的日期格式
        if (document.getCreateTime() != null) {
            docJson.put("createTime", formatDateForES(document.getCreateTime()));
        }
        if (document.getUpdateTime() != null) {
            docJson.put("updateTime", formatDateForES(document.getUpdateTime()));
        }
        
        return docJson;
    }

    /**
     * 构建搜索查询
     */
    private JSONObject buildSearchQuery(DocSearchDTO searchDTO) {
        JSONObject queryObject = new JSONObject();
        
        // 设置分页
        int from = 0;
        int size = 10;
        if (searchDTO.getPageNo() != null && searchDTO.getPageSize() != null) {
            from = (searchDTO.getPageNo() - 1) * searchDTO.getPageSize();
            size = searchDTO.getPageSize();
        }
        queryObject.put("from", from);
        queryObject.put("size", size);
        
        // 构建查询条件
        JSONObject query = new JSONObject();
        JSONObject boolQuery = new JSONObject();
        JSONArray mustQueries = new JSONArray();
        JSONArray filterQueries = new JSONArray();
        
        // 关键词搜索
        if (oConvertUtils.isNotEmpty(searchDTO.getKeyword())) {
            JSONObject multiMatch = new JSONObject();
            JSONObject multiMatchQuery = new JSONObject();
            multiMatchQuery.put("query", searchDTO.getKeyword());
            
            // 设置搜索字段和权重
            JSONArray fields = new JSONArray();
            fields.add("title^3");      // 标题权重最高
            fields.add("description^2"); // 描述权重中等
            fields.add("fileName^1");    // 文件名权重最低
            multiMatchQuery.put("fields", fields);
            multiMatchQuery.put("type", "best_fields");
            multiMatchQuery.put("fuzziness", "AUTO");
            
            multiMatch.put("multi_match", multiMatchQuery);
            mustQueries.add(multiMatch);
        }
        
        // 分类过滤
        if (searchDTO.getCategoryIds() != null && !searchDTO.getCategoryIds().isEmpty()) {
            JSONObject termsQuery = new JSONObject();
            JSONObject categoryTerms = new JSONObject();
            categoryTerms.put("categoryId", searchDTO.getCategoryIds());
            termsQuery.put("terms", categoryTerms);
            filterQueries.add(termsQuery);
        }
        
        // 文件类型过滤
        if (searchDTO.getFileTypes() != null && !searchDTO.getFileTypes().isEmpty()) {
            JSONObject termsQuery = new JSONObject();
            JSONObject fileTypeTerms = new JSONObject();
            fileTypeTerms.put("fileType", searchDTO.getFileTypes());
            termsQuery.put("terms", fileTypeTerms);
            filterQueries.add(termsQuery);
        }
        
        // 状态过滤
        if (searchDTO.getStatuses() != null && !searchDTO.getStatuses().isEmpty()) {
            JSONObject termsQuery = new JSONObject();
            JSONObject statusTerms = new JSONObject();
            statusTerms.put("status", searchDTO.getStatuses());
            termsQuery.put("terms", statusTerms);
            filterQueries.add(termsQuery);
        }
        
        // 公开性过滤
        if (searchDTO.getIsPublic() != null) {
            JSONObject termQuery = new JSONObject();
            JSONObject publicTerm = new JSONObject();
            publicTerm.put("isPublic", searchDTO.getIsPublic());
            termQuery.put("term", publicTerm);
            filterQueries.add(termQuery);
        }
        
        // 时间范围过滤
        if (oConvertUtils.isNotEmpty(searchDTO.getCreateTimeStart()) || 
            oConvertUtils.isNotEmpty(searchDTO.getCreateTimeEnd())) {
            JSONObject rangeQuery = new JSONObject();
            JSONObject timeRange = new JSONObject();
            JSONObject createTimeRange = new JSONObject();
            
            if (oConvertUtils.isNotEmpty(searchDTO.getCreateTimeStart())) {
                createTimeRange.put("gte", searchDTO.getCreateTimeStart());
            }
            if (oConvertUtils.isNotEmpty(searchDTO.getCreateTimeEnd())) {
                createTimeRange.put("lte", searchDTO.getCreateTimeEnd());
            }
            
            timeRange.put("createTime", createTimeRange);
            rangeQuery.put("range", timeRange);
            filterQueries.add(rangeQuery);
        }
        
        // 如果没有查询条件，使用match_all
        if (mustQueries.isEmpty()) {
            JSONObject matchAll = new JSONObject();
            matchAll.put("match_all", new JSONObject());
            mustQueries.add(matchAll);
        }
        
        boolQuery.put("must", mustQueries);
        if (!filterQueries.isEmpty()) {
            boolQuery.put("filter", filterQueries);
        }
        
        query.put("bool", boolQuery);
        queryObject.put("query", query);
        
        // 排序 - 使用正确的字段类型
        JSONArray sort = new JSONArray();
        if (oConvertUtils.isNotEmpty(searchDTO.getSortField())) {
            JSONObject sortField = new JSONObject();
            JSONObject sortOrder = new JSONObject();
            sortOrder.put("order", "desc".equalsIgnoreCase(searchDTO.getSortOrder()) ? "desc" : "asc");
            sortField.put(searchDTO.getSortField(), sortOrder);
            sort.add(sortField);
        } else {
            // 默认按相关度排序，然后按创建时间排序
            JSONObject scoreSort = new JSONObject();
            scoreSort.put("_score", new JSONObject().fluentPut("order", "desc"));
            sort.add(scoreSort);

            JSONObject createTimeSort = new JSONObject();
            JSONObject createTimeSortOrder = new JSONObject();
            createTimeSortOrder.put("order", "desc");
            createTimeSort.put("createTime", createTimeSortOrder);
            sort.add(createTimeSort);
        }
        queryObject.put("sort", sort);
        
        // 高亮设置 - 多字段高亮配置
        if (searchDTO.getHighlight() != null && searchDTO.getHighlight() &&
            oConvertUtils.isNotEmpty(searchDTO.getKeyword())) {
            JSONObject highlight = new JSONObject();
            JSONObject highlightFields = new JSONObject();

            // 高亮标签配置
            JSONObject highlightConfig = new JSONObject();
            highlightConfig.put("pre_tags", new String[]{"<mark>"});
            highlightConfig.put("post_tags", new String[]{"</mark>"});
            highlightConfig.put("fragment_size", 150);  // 高亮片段长度
            highlightConfig.put("number_of_fragments", 3);  // 最多返回3个片段

            // 配置多字段高亮
            highlightFields.put("title", highlightConfig);
            highlightFields.put("description", highlightConfig);
            highlightFields.put("fileName", highlightConfig);

            highlight.put("fields", highlightFields);
            queryObject.put("highlight", highlight);
        }
        
        return queryObject;
    }

    /**
     * 解析搜索结果
     */
    private IPage<DocSearchResultVO> parseSearchResult(JSONObject searchResult, DocSearchDTO searchDTO) {
        int pageNo = searchDTO.getPageNo() == null ? 1 : searchDTO.getPageNo();
        int pageSize = searchDTO.getPageSize() == null ? 10 : searchDTO.getPageSize();
        
        Page<DocSearchResultVO> page = new Page<>(pageNo, pageSize);
        
        if (searchResult == null) {
            return page;
        }
        
        JSONObject hits = searchResult.getJSONObject("hits");
        if (hits == null) {
            return page;
        }
        
        // 设置总数 - ES 9.x格式兼容
        long total = 0;
        Object totalObj = hits.get("total");
        if (totalObj instanceof Number) {
            // ES 7.x格式：直接是数字
            total = ((Number) totalObj).longValue();
        } else if (totalObj instanceof JSONObject) {
            // ES 9.x格式：{"value": 2, "relation": "eq"}
            JSONObject totalJson = (JSONObject) totalObj;
            total = totalJson.getLongValue("value");
        }
        page.setTotal(total);
        
        // 解析文档列表
        JSONArray hitArray = hits.getJSONArray("hits");
        if (hitArray == null || hitArray.isEmpty()) {
            return page;
        }
        
        List<DocSearchResultVO> records = new ArrayList<>();
        for (int i = 0; i < hitArray.size(); i++) {
            JSONObject hit = hitArray.getJSONObject(i);
            JSONObject source = hit.getJSONObject("_source");
            Double score = hit.getDouble("_score");
            
            // 转换为结果对象
            DocSearchResultVO result = convertJsonToSearchResult(source);
            result.setSearchScore(score);
            
            // 处理高亮
            JSONObject highlight = hit.getJSONObject("highlight");
            if (highlight != null) {
                if (highlight.containsKey("title")) {
                    JSONArray titleHighlights = highlight.getJSONArray("title");
                    if (titleHighlights != null && !titleHighlights.isEmpty()) {
                        result.setHighlightTitle(titleHighlights.getString(0));
                    }
                }
                if (highlight.containsKey("description")) {
                    JSONArray descHighlights = highlight.getJSONArray("description");
                    if (descHighlights != null && !descHighlights.isEmpty()) {
                        result.setHighlightDescription(descHighlights.getString(0));
                    }
                }
                if (highlight.containsKey("fileName")) {
                    JSONArray fileNameHighlights = highlight.getJSONArray("fileName");
                    if (fileNameHighlights != null && !fileNameHighlights.isEmpty()) {
                        result.setHighlightFileName(fileNameHighlights.getString(0));
                    }
                }
            }
            
            records.add(result);
        }
        
        page.setRecords(records);
        return page;
    }

    /**
     * 将JSON转换为搜索结果对象
     */
    private DocSearchResultVO convertJsonToSearchResult(JSONObject source) {
        DocSearchResultVO result = new DocSearchResultVO();
        
        result.setId(source.getString("id"));
        result.setTitle(source.getString("title"));
        result.setDescription(source.getString("description"));
        result.setFileName(source.getString("fileName"));
        result.setFileType(source.getString("fileType"));
        result.setFileExt(source.getString("fileExt"));
        result.setFileSize(source.getLong("fileSize"));
        result.setCategoryId(source.getString("categoryId"));
        result.setStatus(source.getInteger("status"));
        result.setIsPublic(source.getInteger("isPublic"));
        result.setIsFeatured(source.getInteger("isFeatured"));
        result.setRatingScore(source.getDouble("ratingScore"));
        result.setDownloadCount(source.getInteger("downloadCount"));
        result.setViewCount(source.getInteger("viewCount"));
        result.setCreateBy(source.getString("createBy"));
        
        // 处理日期字段
        if (source.containsKey("createTime")) {
            result.setCreateTime(source.getDate("createTime"));
        }
        if (source.containsKey("updateTime")) {
            result.setUpdateTime(source.getDate("updateTime"));
        }

        return result;
    }

    /**
     * 删除索引（如果存在）
     */
    private void deleteIndexIfExists() {
        try {
            String url = "http://127.0.0.1:9200/" + DOC_INDEX_NAME;
            org.jeecg.common.util.RestUtil.delete(url);
            log.info("删除已存在的索引: {}", DOC_INDEX_NAME);
        } catch (Exception e) {
            log.debug("索引不存在或删除失败: {}", DOC_INDEX_NAME);
        }
    }

    /**
     * 设置文档映射
     */
    private boolean setDocumentMapping() {
        try {
            JSONObject mapping = createDocumentMapping();
            String url = "http://127.0.0.1:9200/" + DOC_INDEX_NAME + "/_mapping";
            JSONObject result = org.jeecg.common.util.RestUtil.put(url, mapping);
            log.info("设置映射结果: {}", result);
            return result != null && result.getBooleanValue("acknowledged");
        } catch (Exception e) {
            log.error("设置文档映射失败", e);
            return false;
        }
    }

    /**
     * 创建正确的文档映射定义
     */
    private JSONObject createDocumentMapping() {
        JSONObject mapping = new JSONObject();
        JSONObject properties = new JSONObject();

        // 文档ID
        properties.put("id", createKeywordField());

        // 标题 - 支持全文搜索和精确匹配
        properties.put("title", createTextWithKeywordField());

        // 描述 - 支持全文搜索
        properties.put("description", createTextWithKeywordField());

        // 文件名 - 支持全文搜索和精确匹配
        properties.put("fileName", createTextWithKeywordField());

        // 文件类型 - 精确匹配
        properties.put("fileType", createKeywordField());

        // 文件扩展名 - 精确匹配
        properties.put("fileExt", createKeywordField());

        // 文件大小 - 数值类型
        JSONObject fileSize = new JSONObject();
        fileSize.put("type", "long");
        properties.put("fileSize", fileSize);

        // 分类ID - 精确匹配
        properties.put("categoryId", createKeywordField());

        // 状态 - 数值类型
        JSONObject status = new JSONObject();
        status.put("type", "integer");
        properties.put("status", status);

        // 是否公开 - 布尔类型
        JSONObject isPublic = new JSONObject();
        isPublic.put("type", "boolean");
        properties.put("isPublic", isPublic);

        // 是否推荐 - 布尔类型
        JSONObject isFeatured = new JSONObject();
        isFeatured.put("type", "boolean");
        properties.put("isFeatured", isFeatured);

        // 创建时间 - 日期类型（关键修复）
        JSONObject createTime = new JSONObject();
        createTime.put("type", "date");
        createTime.put("format", "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis");
        properties.put("createTime", createTime);

        // 更新时间 - 日期类型
        JSONObject updateTime = new JSONObject();
        updateTime.put("type", "date");
        updateTime.put("format", "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis");
        properties.put("updateTime", updateTime);

        // 创建人 - 精确匹配
        properties.put("createBy", createKeywordField());

        // 上传者ID - 精确匹配
        properties.put("uploaderId", createKeywordField());

        // 统计字段 - 数值类型
        JSONObject downloadCount = new JSONObject();
        downloadCount.put("type", "long");
        properties.put("downloadCount", downloadCount);

        JSONObject viewCount = new JSONObject();
        viewCount.put("type", "long");
        properties.put("viewCount", viewCount);

        JSONObject favoriteCount = new JSONObject();
        favoriteCount.put("type", "long");
        properties.put("favoriteCount", favoriteCount);

        JSONObject ratingScore = new JSONObject();
        ratingScore.put("type", "float");
        properties.put("ratingScore", ratingScore);

        mapping.put("properties", properties);

        log.info("创建文档映射定义: {}", mapping.toJSONString());
        return mapping;
    }

    /**
     * 创建keyword字段定义
     */
    private JSONObject createKeywordField() {
        JSONObject field = new JSONObject();
        field.put("type", "keyword");
        return field;
    }

    /**
     * 创建text字段定义（支持全文搜索和精确匹配）
     */
    private JSONObject createTextWithKeywordField() {
        JSONObject field = new JSONObject();
        field.put("type", "text");
        field.put("analyzer", "standard");

        JSONObject fields = new JSONObject();
        JSONObject keyword = new JSONObject();
        keyword.put("type", "keyword");
        keyword.put("ignore_above", 256);
        fields.put("keyword", keyword);

        field.put("fields", fields);
        return field;
    }

    /**
     * 格式化日期为ES兼容格式
     */
    private String formatDateForES(Date date) {
        if (date == null) {
            return null;
        }
        // ES支持的日期格式：yyyy-MM-dd HH:mm:ss
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 转换为布尔值 - ES需要true/false而不是1/0
     */
    private Boolean convertToBoolean(Integer value) {
        if (value == null) {
            return null;
        }
        return value == 1;
    }

}
